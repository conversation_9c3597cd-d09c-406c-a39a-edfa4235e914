# 🎯 Task 2: Multi-Sensor Threading Practice

## **Your Mission**
Create a multi-sensor monitoring system using threading to simulate monitoring multiple temperature sensors simultaneously. This builds on Task 1 and introduces threading concepts needed for the actual project.

---

## **What You'll Learn**

✅ **Threading basics** - Running multiple tasks simultaneously  
✅ **Thread management** - Starting, stopping, organizing threads  
✅ **Shared data** - How threads communicate safely  
✅ **Real-world simulation** - Multiple sensors like in actual manufacturing  
✅ **Performance benefits** - Why threading matters for sensor monitoring  

---

## **Background: Why Threading?**

In real manufacturing environments:
- You have **multiple machines** to monitor (CNC, 3D printers, sensors)
- Each machine responds at **different speeds**
- You can't wait for one slow machine to block monitoring others
- Data needs to be collected **simultaneously** for accurate timestamping

**Without Threading (Sequential):**
```
Sensor A ────[5s]────> Sensor B ────[3s]────> Sensor C ────[2s]────>
Total time: 10 seconds for one reading cycle
```

**With Threading (Parallel):**
```
Sensor A ────[5s]────>
Sensor B ──[3s]──>
Sensor C ─[2s]─>
Total time: 5 seconds for one reading cycle (much faster!)
```

---

## **Step-by-Step Instructions**

### **Step 1: Copy Required Files**
1. Copy `lib_loggers.py` from task_01 into this folder
2. This ensures your threading code can use the same logging system

### **Step 2: Create Multi-Sensor Configuration**
Create `config.yaml` with multiple sensor definitions:

```yaml
development:
  sensors:
    Temperature_Sensor_A:
      min_temp: 18.0
      max_temp: 35.0
      response_time: 2
      failure_rate: 0.03
    Temperature_Sensor_B:
      min_temp: 15.0
      max_temp: 40.0
      response_time: 1
      failure_rate: 0.02
    Temperature_Sensor_C:
      min_temp: 20.0
      max_temp: 30.0
      response_time: 3
      failure_rate: 0.01
    Pressure_Sensor_1:
      min_temp: 10.0
      max_temp: 50.0
      response_time: 4
      failure_rate: 0.05
  monitoring:
    duration_seconds: 30
    check_interval: 0.5

production:
  sensors:
    Temperature_Sensor_A:
      min_temp: 16.0
      max_temp: 32.0
      response_time: 1
      failure_rate: 0.01
    Temperature_Sensor_B:
      min_temp: 14.0
      max_temp: 38.0
      response_time: 1
      failure_rate: 0.01
  monitoring:
    duration_seconds: 300
    check_interval: 2.0
```

### **Step 3: Create Your Practice Script**
Create `multi_sensor_monitor.py` with this basic structure:

```python
import threading
import time
import random
import yaml
from lib_loggers import set_logger

# Load configuration
try:
    with open("config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The config.yaml file should be present.')

logger = set_logger()

def simulate_sensor_reading(min_temp, max_temp):
    """Simulate reading from a sensor"""
    # Generate a random temperature within sensor range
    temperature = random.uniform(min_temp, max_temp)
    return round(temperature, 1)

def check_temperature_status(temp, min_temp, max_temp):
    """Check if temperature is in acceptable range (reuse from Task 1)"""
    # TODO: Implement using same logic as Task 1
    pass

def monitor_single_sensor(sensor_name, sensor_config, duration):
    """Monitor one sensor for specified duration"""
    logger.info(f"Starting monitoring for {sensor_name}")
    
    # Extract sensor configuration
    min_temp = sensor_config['min_temp']
    max_temp = sensor_config['max_temp']
    response_time = sensor_config['response_time']
    failure_rate = sensor_config['failure_rate']
    
    start_time = time.time()
    reading_count = 0
    
    # TODO: Implement sensor monitoring loop
    # 1. Run for specified duration
    # 2. Simulate sensor response time with time.sleep(response_time)
    # 3. Check for sensor failures using failure_rate
    # 4. Generate and log temperature readings
    # 5. Track statistics
    
    logger.info(f"Completed monitoring for {sensor_name}: {reading_count} readings")
    return reading_count

def main():
    env_choice = 'development'  # Try changing to 'production'
    config_env = config[env_choice]
    
    logger.info("Starting multi-sensor monitoring system...")
    
    # Get monitoring configuration
    duration = config_env['monitoring']['duration_seconds']
    sensors = config_env['sensors']
    
    logger.info(f"Monitoring {len(sensors)} sensors for {duration} seconds")
    
    # TODO: Implement threading
    # 1. Create a dictionary to store threads
    # 2. Create one thread per sensor
    # 3. Start all threads simultaneously
    # 4. Wait for all threads to complete
    # 5. Log summary statistics
    
    logger.info("Multi-sensor monitoring completed")

if __name__ == "__main__":
    main()
```

### **Step 4: Your Coding Tasks**

1. **Complete `check_temperature_status()`**:
   - Reuse logic from Task 1
   - Return "COLD", "HOT", or "NORMAL"

2. **Complete `monitor_single_sensor()`**:
   - Run monitoring loop for specified duration
   - Simulate realistic response times with `time.sleep()`
   - Handle sensor failures randomly
   - Log readings with sensor name prefix
   - Return count of successful readings

3. **Implement threading in `main()`**:
   - Create one thread per sensor using `threading.Thread()`
   - Store threads in a dictionary for management
   - Start all threads simultaneously
   - Use `thread.join()` to wait for completion

### **Step 5: Expected Behavior**

**Console Output (threads running simultaneously):**
```
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting multi-sensor monitoring system...
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Monitoring 4 sensors for 30 seconds
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting monitoring for Temperature_Sensor_A
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting monitoring for Temperature_Sensor_B
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting monitoring for Temperature_Sensor_C
2025-07-31 14:30:15    multi_sensor_monitor    12345    INFO     Starting monitoring for Pressure_Sensor_1
2025-07-31 14:30:16    multi_sensor_monitor    12345    DEBUG    Temperature_Sensor_B: 28.1°C - Status: NORMAL
2025-07-31 14:30:17    multi_sensor_monitor    12345    WARNING  Temperature_Sensor_A: 19.2°C - Status: COLD  
2025-07-31 14:30:17    multi_sensor_monitor    12345    DEBUG    Temperature_Sensor_B: 29.3°C - Status: NORMAL
2025-07-31 14:30:18    multi_sensor_monitor    12345    ERROR    Temperature_Sensor_C: 31.7°C - Status: HOT
2025-07-31 14:30:19    multi_sensor_monitor    12345    CRITICAL Pressure_Sensor_1: Sensor failure detected!
...
```

**Notice:** Messages from different sensors appear interleaved because threads run simultaneously!

---

## **Bonus Challenges** 🏆

### **Challenge 1: Thread-Safe Data Collection**
```python
import threading

# Shared data with lock
shared_data = {}
data_lock = threading.Lock()

def update_sensor_data(sensor_name, reading):
    with data_lock:  # Thread-safe access
        if sensor_name not in shared_data:
            shared_data[sensor_name] = []
        shared_data[sensor_name].append(reading)
```

- Collect all readings in a shared dictionary
- Use locks to prevent data corruption
- Generate a comprehensive report at the end

### **Challenge 2: Graceful Shutdown**
```python
import signal
import sys

shutdown_flag = threading.Event()

def signal_handler(sig, frame):
    logger.info("Shutdown requested...")
    shutdown_flag.set()

signal.signal(signal.SIGINT, signal_handler)  # Handle Ctrl+C

# In sensor monitoring loop:
while not shutdown_flag.is_set() and time.time() - start_time < duration:
    # Monitoring code...
```

- Handle Ctrl+C gracefully
- Stop all threads cleanly
- Generate final report before exit

### **Challenge 3: Dynamic Thread Management**
- Add ability to restart failed sensors
- Implement thread health monitoring
- Add real-time status display

---

## **Key Threading Concepts You'll Practice**

### **1. Creating and Starting Threads**
```python
import threading

def worker_function(name, duration):
    print(f"Worker {name} starting")
    time.sleep(duration)
    print(f"Worker {name} finished")

# Create thread
thread = threading.Thread(target=worker_function, args=("Sensor1", 5))

# Start thread (non-blocking)
thread.start()

# Wait for thread to complete (blocking)
thread.join()
```

### **2. Managing Multiple Threads**
```python
threads = {}

# Start multiple threads
for sensor_name, sensor_config in sensors.items():
    thread = threading.Thread(
        target=monitor_single_sensor, 
        args=(sensor_name, sensor_config, duration)
    )
    threads[sensor_name] = thread
    thread.start()

# Wait for all threads to complete
for sensor_name, thread in threads.items():
    thread.join()
    logger.info(f"Thread for {sensor_name} completed")
```

### **3. Thread-Safe Data Sharing**
```python
import threading

# Global shared data
sensor_readings = {}
readings_lock = threading.Lock()

def add_reading(sensor_name, reading):
    with readings_lock:  # Only one thread can access at a time
        if sensor_name not in sensor_readings:
            sensor_readings[sensor_name] = []
        sensor_readings[sensor_name].append(reading)
```

---

## **Real-World Connection**

This exercise directly prepares you for the actual project:

**Power Meter Project Pattern:**
```python
# From main.py - Multiple equipment monitored simultaneously
threads = {}
for eqpt, (ip, port) in eqpts.items():
    threads[eqpt] = threading.Thread(target=thread_starter, args=(eqpt, ip, port))
    threads[eqpt].start()

# Wait for all monitoring threads
for thread in threads.values():
    thread.join()
```

**Benefits you'll understand:**
- Multiple OPC-UA clients connect to different machines simultaneously
- Each machine has different response times and doesn't block others
- Parallel data collection ensures accurate timestamps
- System continues working even if individual machines fail
- Centralized logging tracks all activities across threads

---

## **Success Criteria**

✅ **Basic Implementation:**
- Multiple sensors monitored simultaneously
- Different response times simulated correctly
- Proper logging with thread identification
- All threads complete successfully

✅ **Advanced Implementation:**
- Thread-safe data collection
- Graceful shutdown handling
- Comprehensive statistics reporting
- Error handling for thread failures

✅ **Professional Implementation:**
- Clean code organization
- Configurable behavior through YAML
- Robust error handling
- Production-ready patterns

---

## **Getting Help**

- Check that all sensors start logging simultaneously (not sequentially)
- Monitor the logs folder for the output file
- Compare timing with single-threaded vs multi-threaded approaches
- Remember: Threading is about concurrency, not necessarily speed

**Ready to master threading? Good luck! 🚀**
