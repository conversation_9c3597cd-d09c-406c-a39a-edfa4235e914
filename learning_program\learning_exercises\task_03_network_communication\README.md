# Task 3: Network Communication & Modbus Practice

## Quick Start Guide

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Start the Simulator
In one terminal:
```bash
python modbus_simulator.py
```

You should see:
```
✅ PowerMeter_A started successfully
✅ PowerMeter_B started successfully

🔌 Modbus simulators running. Press Ctrl+C to stop.
📊 Connect your client to:
   - PowerMeter_A: 127.0.0.1:5020
   - PowerMeter_B: 127.0.0.1:5021
```

### 3. Test Your Client
In another terminal:
```bash
python power_meter_client.py
```

### 4. Your Tasks
Complete the TODO sections in `power_meter_client.py`:

1. **`check_network_connectivity()`** - Test if device is reachable
2. **`decode_float_from_registers()`** - Convert Modbus data to float
3. **`read_power_meter_data()`** - Read data from power meter
4. **`monitor_single_meter()`** - Continuous monitoring loop
5. **`main()`** - Multi-meter threading implementation

### 5. Expected Output
When working correctly, you should see:
```
2025-07-31 15:30:15    power_meter_client    12345    INFO     Starting power meter monitoring system...
2025-07-31 15:30:15    power_meter_client    12345    INFO     Monitoring 2 power meters
2025-07-31 15:30:16    power_meter_client    12345    INFO     PowerMeter_A: L1=231.2V, L2=229.8V, L3=232.1V, Power=6.85kW
```

### 6. Key Learning Points
- **Network connectivity testing** with sockets
- **Modbus TCP protocol** for industrial devices
- **IEEE 754 float conversion** from raw bytes
- **Multi-device monitoring** with threading
- **Error handling** for network failures

### 7. Real-World Connection
This directly prepares you for the actual power meter monitoring project, which uses the same patterns:
- Modbus TCP communication
- IEEE 754 data conversion
- Multi-device threading
- Network error handling

Good luck! 🚀
