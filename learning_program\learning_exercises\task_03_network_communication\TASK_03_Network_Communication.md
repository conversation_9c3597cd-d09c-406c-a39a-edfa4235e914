# 🌐 Task 3: Network Communication & Modbus Practice

## **Your Mission**
Learn network communication fundamentals and implement a Modbus client to read data from simulated power meters. This bridges the gap between threading (Task 2) and the real industrial protocols used in the actual project.

---

## **What You'll Learn**

✅ **Network basics** - IP addresses, ports, and connectivity testing  
✅ **Modbus protocol** - Industrial communication standard for power meters  
✅ **Data conversion** - Converting raw bytes to meaningful measurements  
✅ **Error handling** - Dealing with network failures and timeouts  
✅ **Real-world simulation** - Working with actual industrial data formats  

---

## **Background: Why Network Communication?**

In manufacturing environments:
- **Machines are networked** - Power meters, sensors, controllers all communicate over Ethernet
- **Modbus is everywhere** - Most power meters, PLCs, and industrial devices speak Modbus
- **Data comes as bytes** - Raw network data needs conversion to useful values
- **Networks fail** - Industrial systems must handle connection issues gracefully

**Real Project Connection:**
```python
# From the actual power meter project:
client = ModbusClient(host="*************", port=502)
raw_data = client.read_input_registers(reg_addr=0x0000, reg_nb=2)
voltage = decode_float(raw_data)  # Convert bytes to actual voltage
```

---

## **Step-by-Step Instructions**

### **Step 1: Copy Required Files**
1. Copy `lib_loggers.py` from task_01 into this folder
2. This ensures consistent logging across all tasks

### **Step 2: Install Required Dependencies**
Create `requirements.txt`:
```
pyModbusTCP==0.2.1
PyYAML>=6.0
```

Install with: `pip install -r requirements.txt`

### **Step 3: Create Network Configuration**
Create `config.yaml` with simulated power meter definitions:

```yaml
development:
  power_meters:
    PowerMeter_A:
      ip_address: "127.0.0.1"  # Localhost for simulation
      port: 5020
      device_id: 1
      registers:
        voltage_l1: 0x0000    # Register address for L1 voltage
        voltage_l2: 0x0002    # Register address for L2 voltage  
        voltage_l3: 0x0004    # Register address for L3 voltage
        current_l1: 0x0006    # Register address for L1 current
        power_total: 0x0034   # Register address for total power
    PowerMeter_B:
      ip_address: "127.0.0.1"
      port: 5021
      device_id: 2
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        current_l1: 0x0006
        power_total: 0x0034
  monitoring:
    read_interval: 2.0
    connection_timeout: 3.0
    max_retries: 3

production:
  power_meters:
    PowerMeter_A:
      ip_address: "*************"
      port: 502
      device_id: 1
      registers:
        voltage_l1: 0x0000
        voltage_l2: 0x0002
        voltage_l3: 0x0004
        current_l1: 0x0006
        power_total: 0x0034
  monitoring:
    read_interval: 5.0
    connection_timeout: 5.0
    max_retries: 5
```

### **Step 4: Create Modbus Simulator**
Create `modbus_simulator.py` to simulate power meters:

```python
#!/usr/bin/env python3
"""
Modbus TCP Server Simulator for Power Meter Practice
Simulates realistic power meter data for learning purposes
"""

import time
import math
import random
import threading
from pyModbusTCP.server import ModbusServer, DataBank

class PowerMeterSimulator:
    def __init__(self, port=5020, device_name="PowerMeter_A"):
        self.port = port
        self.device_name = device_name
        self.server = ModbusServer(host="127.0.0.1", port=port, no_block=True)
        self.running = False
        
        # Simulation parameters
        self.base_voltage = 230.0  # Base voltage in volts
        self.base_current = 10.0   # Base current in amps
        self.noise_factor = 0.02   # 2% noise
        
        print(f"Power meter simulator '{device_name}' initialized on port {port}")
    
    def start(self):
        """Start the Modbus server and data simulation"""
        print(f"Starting {self.device_name} simulator on port {self.port}")
        self.server.start()
        self.running = True
        
        # Start data generation thread
        self.data_thread = threading.Thread(target=self._generate_data)
        self.data_thread.daemon = True
        self.data_thread.start()
        
        return self.server.is_run
    
    def stop(self):
        """Stop the simulator"""
        print(f"Stopping {self.device_name} simulator")
        self.running = False
        self.server.stop()
    
    def _generate_data(self):
        """Generate realistic power meter data continuously"""
        print(f"{self.device_name}: Data generation started")
        
        while self.running:
            try:
                # Generate time-varying data with some realism
                time_factor = time.time() / 10.0  # Slow variation
                
                # Simulate 3-phase voltages (slightly different per phase)
                voltage_l1 = self.base_voltage + 5 * math.sin(time_factor) + random.uniform(-2, 2)
                voltage_l2 = self.base_voltage + 5 * math.sin(time_factor + 2.09) + random.uniform(-2, 2)  # 120° phase shift
                voltage_l3 = self.base_voltage + 5 * math.sin(time_factor + 4.19) + random.uniform(-2, 2)  # 240° phase shift
                
                # Simulate currents (with some load variation)
                load_factor = 0.7 + 0.3 * math.sin(time_factor / 3)  # Varying load
                current_l1 = self.base_current * load_factor + random.uniform(-0.5, 0.5)
                current_l2 = self.base_current * load_factor + random.uniform(-0.5, 0.5)
                current_l3 = self.base_current * load_factor + random.uniform(-0.5, 0.5)
                
                # Calculate total power (simplified)
                power_total = (voltage_l1 * current_l1 + voltage_l2 * current_l2 + voltage_l3 * current_l3) / 1000  # kW
                
                # Convert to Modbus register format (IEEE 754 float as 2 registers)
                # TODO: You'll implement these conversion functions
                self._write_float_to_registers(0x0000, voltage_l1)  # L1 Voltage
                self._write_float_to_registers(0x0002, voltage_l2)  # L2 Voltage  
                self._write_float_to_registers(0x0004, voltage_l3)  # L3 Voltage
                self._write_float_to_registers(0x0006, current_l1)  # L1 Current
                self._write_float_to_registers(0x0008, current_l2)  # L2 Current
                self._write_float_to_registers(0x000A, current_l3)  # L3 Current
                self._write_float_to_registers(0x0034, power_total) # Total Power
                
                time.sleep(1.0)  # Update every second
                
            except Exception as e:
                print(f"{self.device_name}: Error in data generation: {e}")
                time.sleep(1.0)
    
    def _write_float_to_registers(self, start_addr, float_value):
        """Convert float to two 16-bit Modbus registers (IEEE 754 format)"""
        # TODO: Implement this function
        # Hint: Use struct.pack() to convert float to bytes, then to registers
        pass

def main():
    """Run multiple power meter simulators"""
    simulators = []
    
    try:
        # Start multiple simulators on different ports
        sim_a = PowerMeterSimulator(port=5020, device_name="PowerMeter_A")
        sim_b = PowerMeterSimulator(port=5021, device_name="PowerMeter_B")
        
        simulators = [sim_a, sim_b]
        
        # Start all simulators
        for sim in simulators:
            if sim.start():
                print(f"✅ {sim.device_name} started successfully")
            else:
                print(f"❌ Failed to start {sim.device_name}")
        
        print("\n🔌 Modbus simulators running. Press Ctrl+C to stop.")
        print("📊 Connect your client to:")
        print("   - PowerMeter_A: 127.0.0.1:5020")
        print("   - PowerMeter_B: 127.0.0.1:5021")
        
        # Keep running until interrupted
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested...")
    finally:
        # Clean shutdown
        for sim in simulators:
            sim.stop()
        print("✅ All simulators stopped")

if __name__ == "__main__":
    main()
```

### **Step 5: Create Your Practice Script**
Create `power_meter_client.py` with this structure:

```python
import time
import struct
import socket
import threading
import yaml
from pyModbusTCP.client import ModbusClient
from lib_loggers import set_logger

# Load configuration
try:
    with open("config.yaml") as config_file:
        config = yaml.safe_load(config_file)
except FileNotFoundError as e:
    raise FileNotFoundError('The config.yaml file should be present.')

logger = set_logger()

def check_network_connectivity(host, port, timeout=3):
    """Test if a device is reachable on the network"""
    # TODO: Implement socket-based connectivity test
    # Hint: Use socket.create_connection() with timeout
    pass

def decode_float_from_registers(register_pair):
    """Convert two Modbus registers back to IEEE 754 float"""
    # TODO: Implement this function
    # This is the reverse of what the simulator does
    # Hint: Look at the actual project's decode_for_float method
    pass

def read_power_meter_data(meter_name, meter_config):
    """Read all configured registers from a power meter"""
    logger.info(f"Connecting to {meter_name} at {meter_config['ip_address']}:{meter_config['port']}")
    
    # TODO: Implement Modbus client connection
    # 1. Create ModbusClient instance
    # 2. Test connectivity first
    # 3. Read each register defined in config
    # 4. Convert raw data to meaningful values
    # 5. Log the results
    # 6. Handle connection errors gracefully
    
    pass

def monitor_single_meter(meter_name, meter_config, duration):
    """Monitor one power meter for specified duration"""
    logger.info(f"Starting monitoring for {meter_name}")
    
    # TODO: Implement continuous monitoring
    # 1. Connect to power meter
    # 2. Read data at configured intervals
    # 3. Handle network failures and reconnection
    # 4. Track statistics (successful reads, failures)
    # 5. Log meaningful data with proper formatting
    
    pass

def main():
    env_choice = 'development'  # Change to 'production' for real meters
    config_env = config[env_choice]
    
    logger.info("Starting power meter monitoring system...")
    
    # Get monitoring configuration
    meters = config_env['power_meters']
    monitoring_config = config_env['monitoring']
    
    logger.info(f"Monitoring {len(meters)} power meters")
    
    # TODO: Implement multi-meter monitoring with threading
    # 1. Create threads for each power meter (like Task 2)
    # 2. Start all monitoring threads simultaneously
    # 3. Handle graceful shutdown
    # 4. Generate summary statistics
    
    logger.info("Power meter monitoring completed")

if __name__ == "__main__":
    main()
```

---

## **Your Coding Tasks**

### **Task 1: Network Connectivity Testing**
Complete `check_network_connectivity()`:
- Use `socket.create_connection()` to test if device is reachable
- Handle timeouts and connection errors
- Return True/False for connectivity status

### **Task 2: Data Conversion Functions**
Complete both conversion functions:
- `_write_float_to_registers()` in simulator (float → registers)
- `decode_float_from_registers()` in client (registers → float)
- Use `struct.pack()` and `struct.unpack()` for IEEE 754 format

### **Task 3: Modbus Client Implementation**
Complete `read_power_meter_data()`:
- Create and configure ModbusClient
- Read input registers for each configured measurement
- Convert raw register data to engineering units
- Handle Modbus exceptions and timeouts

### **Task 4: Continuous Monitoring**
Complete `monitor_single_meter()`:
- Implement monitoring loop with configurable intervals
- Add connection retry logic for network failures
- Track and log statistics (reads, failures, uptime)
- Format output with engineering units (V, A, kW)

### **Task 5: Multi-Meter Threading**
Complete `main()` function:
- Create monitoring threads for each power meter
- Start all threads simultaneously (like Task 2)
- Implement graceful shutdown handling
- Generate comprehensive monitoring report

---

## **Expected Behavior**

**Console Output (with simulator running):**
```
2025-07-31 15:30:15    power_meter_client    12345    INFO     Starting power meter monitoring system...
2025-07-31 15:30:15    power_meter_client    12345    INFO     Monitoring 2 power meters
2025-07-31 15:30:15    power_meter_client    12345    INFO     Connecting to PowerMeter_A at 127.0.0.1:5020
2025-07-31 15:30:15    power_meter_client    12345    INFO     Connecting to PowerMeter_B at 127.0.0.1:5021
2025-07-31 15:30:16    power_meter_client    12345    INFO     PowerMeter_A: L1=231.2V, L2=229.8V, L3=232.1V, Power=6.85kW
2025-07-31 15:30:16    power_meter_client    12345    INFO     PowerMeter_B: L1=230.5V, Power=5.42kW
2025-07-31 15:30:18    power_meter_client    12345    INFO     PowerMeter_A: L1=230.9V, L2=231.3V, L3=229.7V, Power=6.91kW
2025-07-31 15:30:18    power_meter_client    12345    INFO     PowerMeter_B: L1=231.1V, Power=5.38kW
```

**Testing Steps:**
1. Run `python modbus_simulator.py` in one terminal
2. Run `python power_meter_client.py` in another terminal
3. Observe real-time power meter data being read and logged
4. Test network failure by stopping simulator mid-run
5. Verify graceful error handling and reconnection

---

## **Bonus Challenges** 🏆

### **Challenge 1: Data Validation and Quality Control**

#### **The Business Problem: Bad Data Causes Production Issues**

**Real-World Scenario:**
```
Quality Engineer: "The parts from yesterday's batch failed testing. 
                  Was there a power fluctuation during production?"
Current System: "The power meter showed 245V, but that seems wrong..."
Improved System: "ALERT: PowerMeter_A voltage reading 245V exceeds normal 
                 range (220-240V). Flagged as potentially invalid."
```

**Implementation:**
Add data validation to your monitoring:

```python
def validate_power_data(meter_name, measurement_type, value):
    """Validate power meter readings for realistic ranges"""
    validation_ranges = {
        'voltage': (200, 250),    # Volts
        'current': (0, 100),      # Amps  
        'power': (0, 50),         # kW
    }
    
    # TODO: Implement validation logic
    # 1. Check if value is within expected range
    # 2. Check for sudden large changes (> 20% from previous reading)
    # 3. Flag suspicious readings for review
    # 4. Log validation warnings
    pass

def calculate_power_quality_metrics(readings_history):
    """Calculate power quality indicators"""
    # TODO: Implement power quality calculations
    # 1. Voltage stability (standard deviation)
    # 2. Load factor (average vs peak power)
    # 3. Power factor estimation
    # 4. Frequency of out-of-range readings
    pass
```

### **Challenge 2: Historical Data Storage**

#### **The Business Problem: Need Trend Analysis**

**Real-World Need:**
- "Show me power consumption trends over the last week"
- "Which machine uses the most power during night shift?"
- "Are there any patterns in power failures?"

**Implementation:**
Add simple CSV data logging:

```python
import csv
from datetime import datetime

def log_to_csv(meter_name, readings):
    """Store readings in CSV for historical analysis"""
    filename = f"power_data_{meter_name}_{datetime.now().strftime('%Y%m%d')}.csv"
    
    # TODO: Implement CSV logging
    # 1. Create CSV file with headers if it doesn't exist
    # 2. Append new readings with timestamps
    # 3. Include data quality flags
    # 4. Rotate files daily
    pass

def generate_daily_report(meter_name):
    """Generate summary report from CSV data"""
    # TODO: Implement report generation
    # 1. Read today's CSV file
    # 2. Calculate min/max/average values
    # 3. Identify peak usage periods
    # 4. Count data quality issues
    pass
```

---

## **Real-World Connection**

This exercise directly prepares you for the actual project patterns:

**Power Meter Project Similarities:**
```python
# Your practice code:
client = ModbusClient(host="127.0.0.1", port=5020)
raw_data = client.read_input_registers(reg_addr=0x0000, reg_nb=2)
voltage = decode_float_from_registers(raw_data)

# Actual project code:
client = ModbusClient(host=IP_address, port=8899)
response = client.read_input_registers(reg_addr=reg_addr, reg_nb=read_count)
float_value = decode_for_float(response.registers)
```

**Key Skills You'll Master:**
- Industrial network protocols (Modbus TCP)
- Binary data conversion (IEEE 754 floats)
- Network error handling and recovery
- Multi-device monitoring with threading
- Real-time data validation and logging

---

## **Success Criteria**

✅ **Basic Implementation:**
- Successfully connect to simulated power meters
- Read and decode voltage/current/power values
- Handle network timeouts and errors gracefully
- Log data in engineering units (V, A, kW)

✅ **Advanced Implementation:**
- Multi-meter monitoring with threading
- Data validation and quality control
- Historical data storage (CSV)
- Comprehensive error handling and recovery

✅ **Professional Implementation:**
- Production-ready configuration management
- Robust reconnection logic
- Performance monitoring and statistics
- Clean separation of concerns (networking, data processing, logging)

---

## **Getting Help**

**Common Issues:**
- **"Connection refused"**: Make sure simulator is running first
- **"Invalid register data"**: Check your float conversion functions
- **"Timeout errors"**: Verify network connectivity and timeouts
- **"Threading issues"**: Review Task 2 patterns for thread management

**Testing Tips:**
- Start with single meter before adding threading
- Test conversion functions with known values
- Use network tools to verify simulator is listening
- Add debug logging to trace data flow

**Ready to master industrial networking? Good luck! 🚀**
