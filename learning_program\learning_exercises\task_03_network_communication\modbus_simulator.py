#!/usr/bin/env python3
"""
Modbus TCP Server Simulator for Power Meter Practice
Simulates realistic power meter data for learning purposes
"""

import time
import math
import random
import struct
import threading
from pyModbusTCP.server import ModbusServer, DataBank

class PowerMeterSimulator:
    def __init__(self, port=5020, device_name="PowerMeter_A"):
        self.port = port
        self.device_name = device_name
        self.server = ModbusServer(host="127.0.0.1", port=port, no_block=True)
        self.running = False
        
        # Simulation parameters
        self.base_voltage = 230.0  # Base voltage in volts
        self.base_current = 10.0   # Base current in amps
        self.noise_factor = 0.02   # 2% noise
        
        print(f"Power meter simulator '{device_name}' initialized on port {port}")
    
    def start(self):
        """Start the Modbus server and data simulation"""
        print(f"Starting {self.device_name} simulator on port {self.port}")
        self.server.start()
        self.running = True
        
        # Start data generation thread
        self.data_thread = threading.Thread(target=self._generate_data)
        self.data_thread.daemon = True
        self.data_thread.start()
        
        return self.server.is_run
    
    def stop(self):
        """Stop the simulator"""
        print(f"Stopping {self.device_name} simulator")
        self.running = False
        self.server.stop()
    
    def _generate_data(self):
        """Generate realistic power meter data continuously"""
        print(f"{self.device_name}: Data generation started")
        
        while self.running:
            try:
                # Generate time-varying data with some realism
                time_factor = time.time() / 10.0  # Slow variation
                
                # Simulate 3-phase voltages (slightly different per phase)
                voltage_l1 = self.base_voltage + 5 * math.sin(time_factor) + random.uniform(-2, 2)
                voltage_l2 = self.base_voltage + 5 * math.sin(time_factor + 2.09) + random.uniform(-2, 2)  # 120° phase shift
                voltage_l3 = self.base_voltage + 5 * math.sin(time_factor + 4.19) + random.uniform(-2, 2)  # 240° phase shift
                
                # Simulate currents (with some load variation)
                load_factor = 0.7 + 0.3 * math.sin(time_factor / 3)  # Varying load
                current_l1 = self.base_current * load_factor + random.uniform(-0.5, 0.5)
                current_l2 = self.base_current * load_factor + random.uniform(-0.5, 0.5)
                current_l3 = self.base_current * load_factor + random.uniform(-0.5, 0.5)
                
                # Calculate total power (simplified)
                power_total = (voltage_l1 * current_l1 + voltage_l2 * current_l2 + voltage_l3 * current_l3) / 1000  # kW
                
                # Convert to Modbus register format (IEEE 754 float as 2 registers)
                self._write_float_to_registers(0x0000, voltage_l1)  # L1 Voltage
                self._write_float_to_registers(0x0002, voltage_l2)  # L2 Voltage  
                self._write_float_to_registers(0x0004, voltage_l3)  # L3 Voltage
                self._write_float_to_registers(0x0006, current_l1)  # L1 Current
                self._write_float_to_registers(0x0008, current_l2)  # L2 Current
                self._write_float_to_registers(0x000A, current_l3)  # L3 Current
                self._write_float_to_registers(0x0034, power_total) # Total Power
                
                time.sleep(1.0)  # Update every second
                
            except Exception as e:
                print(f"{self.device_name}: Error in data generation: {e}")
                time.sleep(1.0)
    
    def _write_float_to_registers(self, start_addr, float_value):
        """Convert float to two 16-bit Modbus registers (IEEE 754 format)"""
        # Convert float to 4 bytes using IEEE 754 format
        packed_bytes = struct.pack('>f', float_value)  # Big-endian float
        
        # Split into two 16-bit words
        high_word = struct.unpack('>H', packed_bytes[0:2])[0]
        low_word = struct.unpack('>H', packed_bytes[2:4])[0]
        
        # Write to Modbus registers
        DataBank.set_words(start_addr, [high_word, low_word])

def main():
    """Run multiple power meter simulators"""
    simulators = []
    
    try:
        # Start multiple simulators on different ports
        sim_a = PowerMeterSimulator(port=5020, device_name="PowerMeter_A")
        sim_b = PowerMeterSimulator(port=5021, device_name="PowerMeter_B")
        
        simulators = [sim_a, sim_b]
        
        # Start all simulators
        for sim in simulators:
            if sim.start():
                print(f"✅ {sim.device_name} started successfully")
            else:
                print(f"❌ Failed to start {sim.device_name}")
        
        print("\n🔌 Modbus simulators running. Press Ctrl+C to stop.")
        print("📊 Connect your client to:")
        print("   - PowerMeter_A: 127.0.0.1:5020")
        print("   - PowerMeter_B: 127.0.0.1:5021")
        print("\n💡 Example register addresses:")
        print("   - 0x0000-0x0001: L1 Voltage (IEEE 754 float)")
        print("   - 0x0002-0x0003: L2 Voltage (IEEE 754 float)")
        print("   - 0x0004-0x0005: L3 Voltage (IEEE 754 float)")
        print("   - 0x0006-0x0007: L1 Current (IEEE 754 float)")
        print("   - 0x0034-0x0035: Total Power (IEEE 754 float)")
        
        # Keep running until interrupted
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested...")
    finally:
        # Clean shutdown
        for sim in simulators:
            sim.stop()
        print("✅ All simulators stopped")

if __name__ == "__main__":
    main()
