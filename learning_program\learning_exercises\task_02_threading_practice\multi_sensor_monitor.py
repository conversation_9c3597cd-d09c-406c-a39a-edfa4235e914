import threading
import time
import random
import yaml
from lib_loggers import set_logger

# Load configuration
try:
    with open('config.yaml', 'r') as file:
        config = yaml.safe_load(file)
except Exception as e:
    print(f"Error loading config: {e}")

logger = set_logger()

def simulate_sensor_reading(min_temp, max_temp):
    """Simulate reading from a sensor"""
    # Generate a random temperature within sensor range
    temperature = random.uniform(min_temp, max_temp)
    return round(temperature, 1)

def check_temperature_status(temp, min_temp, max_temp):
    """Check if temperature is in acceptable range (reuse from Task 1)"""
    if temp < min_temp:
        return "COLD"
    elif temp > max_temp:
        return "HOT"
    return "NORMAL"

def monitor_single_sensor(sensor_name, sensor_config, duration):
    """Monitor one sensor for specified duration"""
    logger.info(f"Starting monitoring for {sensor_name}")
    
    # Extract sensor configuration
    min_temp = sensor_config['min_temp']
    max_temp = sensor_config['max_temp']
    response_time = sensor_config['response_time']
    failure_rate = sensor_config['failure_rate']
    
    start_time = time.time()
    elapsed_time = 0
    reading_count = 0

    while elapsed_time < duration:
        has_failed = (random.random() < failure_rate)

        if has_failed:
            logger.critical(f"Sensor {sensor_name} failed")
        else:
            temp = simulate_sensor_reading(min_temp, max_temp)
            status = check_temperature_status(temp, min_temp, max_temp)
            logger.debug(f"Sensor {sensor_name} reading: {temp} ({status})")
            reading_count += 1

        time.sleep(response_time)
        elapsed_time = time.time() - start_time
    
    logger.info(f"Completed monitoring for {sensor_name}: {reading_count} readings")
    return reading_count

def main():
    env_choice = 'development'  # Try changing to 'production'
    config_env = config[env_choice]
    
    logger.info("Starting multi-sensor monitoring system...")
    
    # Get monitoring configuration
    duration = config_env['monitoring']['duration_seconds']
    sensors = config_env['sensors']
    
    logger.info(f"Monitoring {len(sensors)} sensors for {duration} seconds")
    
    threads = {}

    # sensor_config becomes a dictionary containing the sensor's configuration
    for sensor_name, sensor_config in sensors.items():
        thread = threading.Thread(target=monitor_single_sensor, args=(sensor_name, sensor_config, duration))
        # Stores thread in dictionary with sensor_name as key and thread as value

        # A thread is a complex data structure that contains:
        # target: the function to be executed by the thread
        # args: the arguments to be passed to the function
        # daemon: whether the thread is a daemon thread or not
        # internal state: the state of the thread (running, waiting, etc.)
        # thread_id: the unique identifier of the thread
        # methods: start(), join(), etc.
        # attributes: name, ident, etc.
        # Each thread object is a separate entity living in the computer memory
        # and the dictionary just stores a reference to the thread object

        threads[sensor_name] = thread
        thread.start()

    for sensor_name, thread in threads.items():
        logger.info(f"Waiting for {sensor_name} to complete...")
        # Upon calling join(), the main thread will wait for the thread to complete
        thread.join()
        logger.info(f"{sensor_name} completed")
    
    logger.info("Multi-sensor monitoring completed")

if __name__ == "__main__":
    main()
